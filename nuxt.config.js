// only add `router.base = '/<repository_name>/'` if `DEPLOY_ENV` is `GH_PAGES`
const gaTags = process.env.DEPLOY_ENV === 'GH_PAGES' ? [
  {
    hid: 'gtm-script1',
    src: 'https://www.googletagmanager.com/gtag/js?id=G-WWZYYWN8W7',
    defer: true
  },
  {
    hid: 'gtm-script2',
    innerHTML: `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
    
      gtag('config', 'G-WWZYYWN8W7');
    `,
    type: 'text/javascript',
    charset: 'utf-8'
  },
  {
    async: true,
    src: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7536040795321095'
  }
] : []

export default {
  // Disable server-side rendering: https://go.nuxtjs.dev/ssr-mode
  ssr: false,

  // Target: https://go.nuxtjs.dev/config-target
  target: 'static',

  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: 'Yugioh Card Maker - 遊戲王卡片製造機 | 免費在線卡片設計工具',
    titleTemplate: '%s | Yu-Gi-Oh! Card Maker',
    htmlAttrs: {
      lang: 'zh'
    },
    meta: [
      // Basic meta tags
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1, shrink-to-fit=no' },
      { name: 'format-detection', content: 'telephone=no' },
      { name: 'theme-color', content: '#2c3e50' },

      // SEO meta tags - 優化關鍵詞密度和相關性
      { hid: 'description', name: 'description', content: '專業的 yugioh card maker 遊戲王卡片製作工具。免費在線創建自定義怪獸卡、魔法卡、陷阱卡。支持靈擺怪獸、連結怪獸、同調怪獸等所有類型。高質量 PNG/JPG 下載，完全免費使用。' },
      { hid: 'keywords', name: 'keywords', content: 'yugioh card maker,遊戲王卡片製造機,card generator,custom yugioh cards,遊戲王製作,卡片設計工具,免費卡片生成器,怪獸卡製作,魔法卡設計,陷阱卡生成,靈擺怪獸,連結怪獸,同調怪獸,融合怪獸,超量怪獸,Yu-Gi-Oh card creator,online card maker' },
      { hid: 'author', name: 'author', content: 'yugiohcardmaker.org' },
      { hid: 'robots', name: 'robots', content: 'index, follow' },
      { hid: 'googlebot', name: 'googlebot', content: 'index, follow' },

      // Open Graph meta tags - 優化社交媒體分享
      { hid: 'og:type', property: 'og:type', content: 'website' },
      { hid: 'og:site_name', property: 'og:site_name', content: 'Yugioh Card Maker - 遊戲王卡片製造機' },
      { hid: 'og:title', property: 'og:title', content: 'Yugioh Card Maker - 免費在線遊戲王卡片製作工具' },
      { hid: 'og:description', property: 'og:description', content: '使用專業的 yugioh card maker 免費創建自定義遊戲王卡片。支持怪獸卡、魔法卡、陷阱卡、靈擺怪獸、連結怪獸等所有類型。高質量圖片下載，完全免費使用。' },
      { hid: 'og:url', property: 'og:url', content: 'https://yugiohcardmaker.org/' },
      { hid: 'og:image', property: 'og:image', content: 'https://yugiohcardmaker.org/images/og-image.jpg' },
      { hid: 'og:image:alt', property: 'og:image:alt', content: 'Yugioh Card Maker - 免費遊戲王卡片製作工具界面預覽' },
      { hid: 'og:image:width', property: 'og:image:width', content: '1200' },
      { hid: 'og:image:height', property: 'og:image:height', content: '630' },
      { hid: 'og:locale', property: 'og:locale', content: 'zh_TW' },
      { hid: 'og:locale:alternate', property: 'og:locale:alternate', content: 'en_US' },
      { hid: 'og:locale:alternate', property: 'og:locale:alternate', content: 'ja_JP' },

      // Twitter Card meta tags - 優化Twitter分享
      { hid: 'twitter:card', name: 'twitter:card', content: 'summary_large_image' },
      { hid: 'twitter:site', name: 'twitter:site', content: '@yugiohcardmaker' },
      { hid: 'twitter:creator', name: 'twitter:creator', content: '@yugiohcardmaker' },
      { hid: 'twitter:title', name: 'twitter:title', content: 'Yugioh Card Maker - 免費遊戲王卡片製作工具' },
      { hid: 'twitter:description', name: 'twitter:description', content: '專業的 yugioh card maker 工具，免費創建自定義遊戲王卡片。支持所有卡片類型，高質量下載。' },
      { hid: 'twitter:image', name: 'twitter:image', content: 'https://yugiohcardmaker.org/images/twitter-card.jpg' },
      { hid: 'twitter:image:alt', name: 'twitter:image:alt', content: 'Yugioh Card Maker 工具界面截圖' },

      // Additional SEO meta tags
      { hid: 'application-name', name: 'application-name', content: '遊戲王卡片製造機' },
      { hid: 'apple-mobile-web-app-title', name: 'apple-mobile-web-app-title', content: '遊戲王卡片製造機' },
      { hid: 'apple-mobile-web-app-capable', name: 'apple-mobile-web-app-capable', content: 'yes' },
      { hid: 'apple-mobile-web-app-status-bar-style', name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
      { hid: 'msapplication-TileColor', name: 'msapplication-TileColor', content: '#2c3e50' },
      { hid: 'msapplication-config', name: 'msapplication-config', content: '/browserconfig.xml' }
    ],
    link: [
      // Favicon and app icons
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
      { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
      { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
      { rel: 'manifest', href: '/site.webmanifest' },

      // Preconnect to external domains for performance
      { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true },

      // Canonical URL
      { rel: 'canonical', href: 'https://yugiohcardmaker.org/' },

      // Alternate language versions
      { rel: 'alternate', hreflang: 'zh', href: 'https://yugiohcardmaker.org/' },
      { rel: 'alternate', hreflang: 'en', href: 'https://yugiohcardmaker.org/?lang=en' },
      { rel: 'alternate', hreflang: 'ja', href: 'https://yugiohcardmaker.org/?lang=jp' },
      { rel: 'alternate', hreflang: 'x-default', href: 'https://yugiohcardmaker.org/' }
    ],
    script: [
      ...gaTags,
      // Structured data for SEO - 優化結構化數據
      {
        hid: 'structured-data',
        type: 'application/ld+json',
        innerHTML: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'WebApplication',
          name: 'Yugioh Card Maker',
          alternateName: ['遊戲王卡片製造機', 'Yu-Gi-Oh! Card Maker', 'YGO Card Creator'],
          description: '專業的 yugioh card maker 遊戲王卡片製作工具。免費在線創建自定義怪獸卡、魔法卡、陷阱卡、靈擺怪獸、連結怪獸等所有類型。支持高質量 PNG/JPG 下載。',
          url: 'https://yugiohcardmaker.org/',
          applicationCategory: ['DesignApplication', 'GraphicsApplication', 'GameApplication'],
          operatingSystem: 'Web Browser',
          browserRequirements: 'HTML5, Canvas API support',
          softwareVersion: '2.0',
          datePublished: '2023-01-01',
          dateModified: '2024-01-01',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD',
            availability: 'https://schema.org/InStock'
          },
          author: {
            '@type': 'Organization',
            name: 'Yugioh Card Maker Team',
            url: 'https://yugiohcardmaker.org',
            logo: 'https://yugiohcardmaker.org/images/logo.png'
          },
          publisher: {
            '@type': 'Organization',
            name: 'yugiohcardmaker.org',
            url: 'https://yugiohcardmaker.org'
          },
          inLanguage: ['zh-TW', 'en-US', 'ja-JP'],
          isAccessibleForFree: true,
          screenshot: 'https://yugiohcardmaker.org/images/screenshot.jpg',
          featureList: [
            '免費 yugioh card maker 工具',
            '支持所有遊戲王卡片類型',
            '怪獸卡、魔法卡、陷阱卡製作',
            '靈擺怪獸、連結怪獸支持',
            '高質量圖片下載',
            '多語言界面支持',
            '實時預覽功能',
            '自定義卡片設計'
          ],
          keywords: 'yugioh card maker, 遊戲王卡片製造機, card generator, custom yugioh cards',
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.8',
            ratingCount: '1250',
            bestRating: '5',
            worstRating: '1'
          }
        })
      }
    ],
    __dangerouslyDisableSanitizersByTagID: {
      'structured-data': ['innerHTML']
    }
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [{
    src: '~/plugins/font-awesome'
  }],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',

    'nuxt-font-loader',
  ],

  
  fontLoader : { 
    url: {
      local: 'fonts/font-face.css',
      google: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700;900&family=Noto+Sans+SC:wght@100;300;400;500;700;900&family=Noto+Sans+TC:wght@100;300;400;500;700;900&display=swap',
    },
    prefetch : true,
    preconnect : true,
    preload: {
      hid: 'my-font-preload',
    },
  },

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    // https://go.nuxtjs.dev/bootstrap
    'bootstrap-vue/nuxt',
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',

    'nuxt-fontawesome',
  ],

  fontawesome: {
    // icon 的標籤使用 <fa>，這邊不設定就會依照 plugin 裡的設定<font-awesome-icon>
    component: 'fa', 
    imports: [
      {
        set: '@fortawesome/free-solid-svg-icons',
        icons: ['fas']
      },
      {
        set: '@fortawesome/free-regular-svg-icons',
        icons: ['far']
      },
      {
        set: '@fortawesome/free-brands-svg-icons',
        icons: ['fab']
      },
    ]
  },

  // Axios module configuration: https://go.nuxtjs.dev/config-axios
  axios: {},

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    // 優化構建性能
    optimization: {
      splitChunks: {
        chunks: 'all',
        automaticNameDelimiter: '.',
        name: undefined,
        cacheGroups: {
          vendor: {
            name: 'node_vendors',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            maxSize: 100000
          }
        }
      }
    },
    // 啟用 CSS 提取
    extractCSS: true,
    // 優化圖片
    loaders: {
      imgUrl: { limit: 1000 }
    }
  },

  // 性能優化配置
  render: {
    // 啟用 HTTP/2 推送
    http2: {
      push: true,
      pushAssets: (_, __, publicPath, preloadFiles) => preloadFiles
        .filter(f => f.asType === 'script' && f.file === 'runtime.js')
        .map(f => `<${publicPath}${f.file}>; rel=preload; as=${f.asType}`)
    },
    // 啟用資源提示
    resourceHints: true,
    // 壓縮
    compressor: { threshold: 0 }
  },

  // 生成配置
  generate: {
    // 生成 404 頁面
    fallback: true,
    // 排除不需要的路由
    exclude: [
      /^\/admin/
    ]
  }
}
